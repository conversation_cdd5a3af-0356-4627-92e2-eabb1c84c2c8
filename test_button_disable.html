<!DOCTYPE html>
<html>
<head>
    <title>测试按钮禁用功能</title>
</head>
<body>
    <h1>测试按钮禁用功能</h1>
    <p>测试条件：当 status == 2 时，修改按钮应该被隐藏</p>
    
    <h2>测试数据：</h2>
    <ul>
        <li>状态 0 (待完成) - 修改按钮应该显示</li>
        <li>状态 1 (已完成) - 修改按钮应该显示</li>
        <li>状态 2 (已废弃) - 修改按钮应该隐藏</li>
    </ul>
    
    <h2>测试表达式：</h2>
    <pre>
disabled: '$status == 2'
    </pre>
    
    <h2>测试结果：</h2>
    <script>
        // 模拟测试数据
        const testData = [
            { id: 1, title: '任务1', status: 0 },
            { id: 2, title: '任务2', status: 1 },
            { id: 3, title: '任务3', status: 2 }
        ];
        
        // 模拟条件评估函数
        function evaluateCondition(condition, rowData) {
            if (!condition) return false;
            
            try {
                let expression = condition.replace(/\$(\w+)/g, (match, fieldName) => {
                    const value = rowData[fieldName];
                    return typeof value === 'string' ? `"${value}"` : value;
                });
                
                return new Function('return ' + expression)();
            } catch (error) {
                console.error('条件表达式评估失败：', error, condition);
                return false;
            }
        }
        
        // 测试
        const condition = '$status == 2';
        testData.forEach(data => {
            const shouldDisable = evaluateCondition(condition, data);
            console.log(`任务${data.id} (状态${data.status}): ${shouldDisable ? '隐藏' : '显示'} 修改按钮`);
            document.write(`<p>任务${data.id} (状态${data.status}): ${shouldDisable ? '隐藏' : '显示'} 修改按钮</p>`);
        });
    </script>
</body>
</html>
