<!--
 * @author: 吉慧雯
 * @name: 计划任务
 * @desc: 介绍
 * @LastEditTime: 2022-12-30 15:55:07
 * @FilePath: \eyc3_guard_pc/src/views/work/basic/plan
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        :columns="columns"
        :formitems="formitems"
        :buttonList="buttonList"
        @info-data="infodata"
    >
        <template #descriptions-footer>
            <el-main class="info">
                <div class="content">
                    <div class="basics">
                        <div class="title">基础</div>
                        <el-divider />
                        <div class="btns">
                            <el-form
                                :model="infodatas"
                                label-width="120px"
                                label-position="top"
                            >
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="人员">
                                            {{ infodatas.names }}
                                        </el-form-item>
                                    </el-col>
                                    <el-col
                                        :span="8"
                                        :offset="3"
                                    >
                                        <el-form-item label="任务分组">
                                            {{ infodatas.group_title }}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-form-item label="任务时间">
                                    {{ infodatas.publish_time ? `${infodatas.publish_time}上线` : '暂未开始' }}
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>
                    <div class="task">
                        <div class="title">任务内容</div>
                        <el-divider />
                        <div class="stitle">{{ infodatas.title }}</div>
                        <div class="desc">
                            {{ infodatas.content }}
                        </div>
                        <div
                            class="accessory"
                            v-if="infodatas.file && infodatas.files.length != 0"
                        >
                            <div class="stitle">任务附件</div>
                            <template
                                v-for="(el, i) in infodatas.files"
                                :key="i"
                            >
                                <div class="file">
                                    <img
                                        :src="el.url"
                                        alt=""
                                        @click="openFile(el.url)"
                                    />
                                    <div
                                        class="name"
                                        @click="openFile(el.url)"
                                    >
                                        {{ el.name }}
                                    </div>

                                    <div
                                        class="download"
                                        @click="download(el)"
                                    >
                                        下载
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div class="flow">
                            <div class="stitle">任务流程</div>
                            <el-timeline>
                                <el-timeline-item
                                    v-for="(activity, index) in infodatas.process"
                                    :key="index"
                                    :timestamp="activity.created_at"
                                >
                                    {{ activity.user_name }}{{ activity.action }}
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                    </div>
                </div>
                <div class="footer">
                    <template v-if="infodatas.status == 0">
                        <el-button
                            type="primary"
                            round
                            icon="el-icon-check"
                            :loading="loadingFinish"
                            v-if="ISjurisdiction('任务分配','结束任务')"
                            @click="finishTask"
                            >结束任务</el-button
                        >
                        <el-button
                            type="danger"
                            round
                            icon="el-icon-close"
                            @click="reProject"
                              v-if="ISjurisdiction('任务分配','重新立项')"
                            :loading="loadingReProject"
                            >重新立项</el-button
                        >
                    </template>
                    <template v-else-if="infodatas.status == 1">
                        <el-button
                            type="info"
                            round
                            disabled
                            >已完成</el-button
                        >
                    </template>
                    <template v-else-if="infodatas.status == 2">
                        <el-button
                            type="info"
                            round
                            disabled
                            >已废弃</el-button
                        >
                    </template>
                </div>
            </el-main>
        </template>
    </yp_list>
</template>

<script>
import { ISjurisdiction } from '@/utils/commit'
import useTabs from '@/utils/useTabs'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'BasicPlan',
    data() {
        return {
            url: 'task/get_ls',
            columns: [
                {
                    label: '任务名称',
                    prop: 'title'
                },
                {
                    label: '任务优先度',
                    component: 'taskLevel',
                },
                {
                    label: '创建人员',
                    prop: 'user_name'
                },
                {
                    label: '创建时间',
                    prop: 'created_at'
                },
                {
                    label: '任务分组',
                    prop: 'group_title'
                },
                {
                    label: '状态',
                    prop: 'status',
                    component: 'tag',
                    options: [
                        {
                            label: '待完成',
                            type: '0',
                            mode: 'warning'
                        },
                        {
                            label: '已完成',
                            type: '1',
                            mode: 'success'
                        },
                        {
                            label: '已废弃',
                            type: '2',
                            mode: 'info'
                        }
                    ]
                },
                {
                    label: '操作',
                    prop: 'action',
                    component: 'button',
                    options: [
                        {
                            label: '详情',
                            component: 'detail',
                            options: {
                                title: '任务详情',
                                remote: {
                                    api: 'task/get_info', // 获取详情接口
                                    data: {
                                        // 获取详情接口数据
                                        id: '$id'
                                    }
                                },
                                haveslot: true
                            }
                        },
                        {
                            label: '修改',
                            component: 'form',
                            disabled: '$status == 2', // 当状态为已废弃时禁用
                            options: {
                                name: 'task_assign_edit', // 跳转页面名
                                remote: {
                                    state: 'edit', // 状态,'add'|'edit'|'detail'
                                    label: '编辑任务分配', // 页头名
                                    api: 'group/get_info', // 获取详情接口
                                    edit: 'group/post_modify', // 修改详情接口
                                    data: {
                                        // 获取详情接口数据
                                        id: '$id'
                                    },
                                    backrouter: '/basic/task_group'
                                }
                            }
                        },
                        {
                            label: '删除',
                            type: 'danger',
                            component: 'confirm',
                            options: {
                                label: '确认删除',
                                message: '是否确认删除?',
                                remote: {
                                    api: 'task/post_del',
                                    data: {
                                        id: '$id'
                                    }
                                }
                            }
                        }
                    ]
                }
            ],
            formitems: [
                {
                    label: '完成状态',
                    name: 'is_all',
                    component: 'select',
                    value: 0,
                    options: {
                        placeholder: '请输入',
                        items: [
                            {
                                label: '未完成',
                                value: 0
                            },
                            {
                                label: '全部',
                                value: 1
                            }
                        ]
                    }
                }
            ],
            buttonList: [
                {
                    label: '新建',
                    component: 'form',
                    options: {
                        name: 'task_assign_add', // 跳转页面名
                        icon: 'el-icon-Plus', // 按钮图标
                        remote: {
                            state: 'add', // 状态
                            label: '新增任务', // 页头名
                            api: 'group/post_add', // 新增地址接口
                            data: {}
                        }
                    }
                }
            ],
            infodatas: {},
            resourceName: '资源不存在',
            loadingFinish: false,
            loadingReProject: false
        }
    },
    methods: {
        ISjurisdiction,
        infodata(infodata) {
            this.infodatas = JSON.parse(infodata)
            if (this.infodatas.file) {
                this.infodatas.files = JSON.parse(this.infodatas.file)
            }
            this.infodatas.names = `${this.infodatas.flow.title}，${this.infodatas.flow.finish}/${this.infodatas.flow.total}已完成`
        },
        finishTask() {
            this.loadingFinish = true
            this.$HTTP
                .post('task/post_finish', {
                    id: this.infodatas.id
                })
                .then((res) => {
                    if (res.errcode == 0) {
                        ElMessage.success('提交成功')
                        // this.infodatas.status = 0
                        useTabs.refresh()
                        // useTabs.close()
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                    this.loadingFinish = false
                })
                .finally(() => {})
        },
        reProject() {
            this.loadingReProject = true
            this.$HTTP
                .post('task/post_re_project', {
                    id: this.infodatas.id
                })
                .then((res) => {
                    if (res.errcode == 0) {
                        ElMessage.success('提交成功')
                        this.$router.push({ name: 'task_assign_add' })
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                    this.loadingReProject = false
                })
                .finally(() => {})
        },
        // 预览文件
        openFile(url) {
            window.open(url)
        },
        // 下载文件
        download(el) {
            this.$TOOL.runtime.biz.util.downloadFile({
                url: el.url, //要下载的文件的url
                name: el.name, //定义下载文件名字
                onProgress: function (msg) {
                    // 文件下载进度回调
                },
                onSuccess: function (result) {
                    /*
          true
        */
                },
                onFail: function () {}
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.info {
    position: relative;
    height: 90vh;
    width: 100%;
    overflow: hidden;
}

.content {
    height: 100vh;
    box-sizing: border-box;
    overflow: scroll;
    padding-bottom: 200px;
}
.basics {
    // padding: 0 20px;
    width: 100%;
    .title {
        // margin-bottom: 20px;
        font-size: 16px;
        font-weight: 550;
    }

    .btns {
        // margin-top: 30px;

        // .item {
        //     display: flex;
        //     padding: 9px 16px;
        //     margin-bottom: 12px;
        //     width: 40%;
        //     line-height: 16px;
        //     border-radius: 17px;
        //     border: 1px solid #eaebed;
        // }
    }
}

.task {
    .title {
        // margin-bottom: 20px;
        font-size: 16px;
        font-weight: 550;
    }

    .stitle {
        margin-bottom: 16px;
        font-size: 16px;
    }
    .accessory {
        margin-top: 30px;
        .file {
            position: relative;
            display: flex;
            align-content: center;
            margin-top: 12px;
            background-color: #ebf5ff;
            padding: 8px;
            width: 60%;
            line-height: 45px;
            border-radius: 8px;

            img {
                margin-right: 8px;
                width: 45px;
                height: 45px;
                border-radius: 4px;
            }

            .name {
                width: 80%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .download {
                position: absolute;
                right: 4px;
                color: #409eff;
                cursor: pointer;
            }
        }
    }

    .flow {
        margin-top: 20px;
        margin-left: 2px;
    }
}

.footer {
    background-color: #fff;
    padding-top: 20px;
    padding-left: 16px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    border-top: 1px solid #dcdfe6;
}
</style>
